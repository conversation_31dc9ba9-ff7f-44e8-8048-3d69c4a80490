// 合作商数据类型定义
export interface Partner {
  id: string;
  name: string;
  logo: string;
  website?: string;
  description?: string;
}

// 默认合作商数据配置
export const defaultPartners: Partner[] = [
  { id: '1', name: '一个木函', logo: 'https://alapi.s3.bitiful.net/compony_logo/woobx.png', website: 'https://woobx.cn/' },
  { id: "2", name: "腾讯云", logo: "https://alapi.s3.bitiful.net/compony_logo/tencentcloud.png", website: "https://cloud.tencent.com/" },
  { id: "3", name: "阿里云", logo: "https://alapi.s3.bitiful.net/compony_logo/aliyun.png", website: "https://www.aliyun.com/" },
  { id: "5", name: "火山引擎", logo: "https://alapi.s3.bitiful.net/compony_logo/huoshan.png", website: "https://volcengine.com/" },
  { id: "6", name: "百度智能云", logo: "https://alapi.s3.bitiful.net/compony_logo/baidu_bce.png", website: "https://cloud.baidu.com/" },
  { id: "7", name: "华为云", logo: "https://alapi.s3.bitiful.net/compony_logo/huaweiyun.png", website: "https://www.huaweicloud.com/" },
  { id: "8", name: "网易", logo: "https://alapi.s3.bitiful.net/compony_logo/163.png", website: "https://www.163.com/" },
  { id: "9", name: "抖音", logo: "https://alapi.s3.bitiful.net/compony_logo/douyin.png", website: "https://www.douyin.com/" },
  { id: "10", name: "快手", logo: "https://alapi.s3.bitiful.net/compony_logo/kuaishou.png", website: "https://www.kuaishou.com/" },
  { id: "11", name: "哔哩哔哩", logo: "https://alapi.s3.bitiful.net/compony_logo/bilibili.png", website: "https://bilibili.com/" },
  { id: "12", name: "知乎", logo: "https://alapi.s3.bitiful.net/compony_logo/zhihu.png", website: "https://www.zhihu.com/" },
  { id: "13", name: "DeepSeek", logo: "https://alapi.s3.bitiful.net/compony_logo/deepseek.png", website: "https://www.deepseek.com/" },
  { id: "14", name: "小米", logo: "https://alapi.s3.bitiful.net/compony_logo/xm.png", website: "https://mi.com/" },
  { id: "15", name: "OPPO", logo: "https://alapi.s3.bitiful.net/compony_logo/oppo.png", website: "https://www.oppo.com/" },
  { id: "16", name: "VIVO", logo: "https://alapi.s3.bitiful.net/compony_logo/vivo.png", website: "https://www.vivo.com.cn/" },
  { id: "17", name: "360", logo: "https://alapi.s3.bitiful.net/compony_logo/360.png", website: "https://www.360.cn/" },
  { id: "18", name: "微软", logo: "https://alapi.s3.bitiful.net/compony_logo/micrsoft.png", website: "https://www.microsoft.com/" },
  { id: "19", name: "中国电信", logo: "https://alapi.s3.bitiful.net/compony_logo/zgdx.png", website: "https://www.189.cn/" },
  { id: "20", name: "中国移动", logo: "https://alapi.s3.bitiful.net/compony_logo/zglt.png", website: "https://www.chinaunicom.cn/" },
  { id: "21", name: "中国联通", logo: "https://alapi.s3.bitiful.net/compony_logo/zglt.png", website: "https://www.chinaunicom.cn/" },
  { id: "21", name: "OPENAI", logo: "https://alapi.s3.bitiful.net/compony_logo/openai.png", website: "https://www.openai.com/" },
  { id: "22", name: "CNB.COOL", logo: "https://alapi.s3.bitiful.net/compony_logo/cnb.png", website: "https://cnb.cool" },
];

// 合作商配置选项
export const partnerConfig = {
  // 每行显示的合作商数量
  partnersPerRow: 6,
  // 显示的行数
  rows: 3,
  // 每行重复次数（用于无缝滚动）
  repeatTimes: 3,
  // 动画配置
  animation: {
    baseDuration: 30, // 基础动画时长（秒）
    durationIncrement: 5, // 每行递增时长（秒）
    baseDelay: 2, // 基础延迟时间（秒）
  },
  // 响应式断点配置
  breakpoints: {
    desktop: {
      cardWidth: 192, // w-48
      cardHeight: 96, // h-24
      margin: 16, // mx-4
    },
    tablet: {
      cardWidth: 160,
      cardHeight: 80,
      margin: 8,
    },
    mobile: {
      cardWidth: 140,
      cardHeight: 70,
      margin: 6,
    },
  },
};

// 工具函数：创建行数据
export const createRowData = (partners: Partner[], startIndex: number, config = partnerConfig) => {
  const rowPartners: Partner[] = [];
  const totalItems = config.partnersPerRow * config.repeatTimes;

  for (let i = 0; i < totalItems; i++) {
    const partnerIndex = (startIndex + i) % partners.length;
    rowPartners.push({
      ...partners[partnerIndex],
      id: `${partners[partnerIndex].id}-row${startIndex}-${i}`
    });
  }

  return rowPartners;
};

// 工具函数：生成所有行数据
export const generatePartnerRows = (partners: Partner[] = defaultPartners, config = partnerConfig) => {
  return Array.from({ length: config.rows }, (_, index) =>
    createRowData(partners, index * config.partnersPerRow, config)
  );
};
