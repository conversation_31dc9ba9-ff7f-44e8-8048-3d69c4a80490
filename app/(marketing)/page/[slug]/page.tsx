import React from "react";
import { Divider } from "antd";
import { getPageContent, getSystemConfig } from "@/app/actions";
import { notFound } from "next/navigation";
import type { Metadata } from "next";
import { SiteConfig } from "@/app/types";
import { Clock, Calendar, FileText } from "lucide-react";
import "./rich-content.css";

// 定义页面参数类型
interface PageParams {
  params: { slug: string };
}

export async function generateMetadata({ params }: PageParams): Promise<Metadata> {
  const page = await getPageContent(params.slug);
  const siteConfig = await getSystemConfig() as SiteConfig;

  if (!page) {
    return {
      title: "页面未找到 - " + siteConfig.site_name,
      description: "您访问的页面不存在",
      applicationName: siteConfig.site_name,
      icons: {
        icon: {
          url: "/images/logo/icon_logo.svg"
        }
      }
    };
  }

  return {
    title: page.title + " - " + siteConfig.site_name,
    keywords: siteConfig.site_keywords,
    description: (page.description || page.title) + " - " + siteConfig.site_description,
    applicationName: siteConfig.site_name,
    openGraph: {
      title: page.title,
      description: page.description || page.title,
      siteName: siteConfig.site_name,
      type: 'article',
    },
    icons: {
      icon: {
        url: "/images/logo/icon_logo.svg"
      }
    }
  };
}

// 静态页面组件
export default async function StaticPage({ params }: PageParams) {
  // 验证 slug 参数
  if (!params.slug) {
    notFound();
  }

  // 获取页面内容
  const page = await getPageContent(params.slug);
  if (!page) {
    notFound();
  }

  // 获取站点配置
  const siteConfig = await getSystemConfig() as SiteConfig;

  // 计算阅读时间（基于中文字符数）
  const readingTime = Math.ceil((page.content?.length || 0) / 400);

  // 格式化更新时间
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <div className="bg-white border-b border-gray-200">
        <div className="w-full max-w-6xl mx-auto lg:px-0 px-4 py-12">
          <div className="text-center">
            {/* 页面类型标识 */}
            <div className="flex items-center justify-center mb-4">
              <FileText className="h-5 w-5 text-gray-500 mr-2" />
              <span className="text-sm text-gray-500 font-medium">政策文档</span>
            </div>

            {/* 页面标题 */}
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              {page.title}
            </h1>

            {/* 页面描述 */}
            {page.description && (
              <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
                {page.description}
              </p>
            )}

            {/* 页面元信息 */}
            <div className="flex flex-wrap items-center justify-center gap-4 text-sm text-gray-500">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                <span>更新时间: {formatDate(page.updated_at)}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                <span>阅读时间: 约 {readingTime} 分钟</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="w-full max-w-6xl mx-auto lg:px-0 px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* 内容主体 */}
          <div className="p-6 sm:p-10">
            <article className="prose prose-lg max-w-none
              prose-headings:text-gray-900 prose-headings:font-semibold prose-headings:tracking-tight
              prose-p:text-gray-700 prose-p:leading-relaxed
              prose-a:text-gray-600 prose-a:no-underline hover:prose-a:text-gray-900 hover:prose-a:underline
              prose-strong:text-gray-900 prose-strong:font-semibold
              prose-ul:text-gray-700 prose-ol:text-gray-700
              prose-li:text-gray-700 prose-li:leading-relaxed
              prose-blockquote:text-gray-700 prose-blockquote:border-gray-300
              prose-code:text-gray-800 prose-code:bg-gray-100 prose-code:px-1 prose-code:py-0.5 prose-code:rounded
              prose-pre:bg-gray-50 prose-pre:border prose-pre:border-gray-200
              prose-img:rounded-lg prose-img:shadow-sm
              prose-hr:border-gray-200
              prose-table:text-gray-700
              prose-th:text-gray-900 prose-th:font-semibold
              prose-td:text-gray-700">
              <div
                dangerouslySetInnerHTML={{ __html: page.content }}
                className="rich-content"
              />
            </article>
          </div>
        </div>

        {/* 页面底部信息 */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="text-sm text-gray-600 space-y-2">
              <p>如有任何问题或建议，请联系我们的客服团队</p>
              <Divider className="my-4 border-gray-200" />
              <div className="flex flex-col sm:flex-row items-center justify-center gap-2 text-xs text-gray-500">
                <span>© {new Date().getFullYear()} {siteConfig.site_name}</span>
                <span className="hidden sm:inline">•</span>
                <span>最后更新: {formatDate(page.updated_at)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}