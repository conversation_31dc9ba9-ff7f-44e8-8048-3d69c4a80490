/* 富文本内容样式优化 */
.rich-content {
  /* 确保链接颜色为灰色 */
  --tw-prose-links: rgb(75 85 99); /* gray-600 */
  --tw-prose-links-hover: rgb(17 24 39); /* gray-900 */
}

.rich-content a {
  color: rgb(75 85 99) !important; /* gray-600 */
  text-decoration: none !important;
  transition: color 0.2s ease-in-out;
}

.rich-content a:hover {
  color: rgb(17 24 39) !important; /* gray-900 */
  text-decoration: underline !important;
}

/* 优化表格样式 */
.rich-content table {
  border-collapse: collapse;
  margin: 1.5rem 0;
  width: 100%;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

.rich-content table thead {
  background-color: rgb(249 250 251); /* gray-50 */
}

.rich-content table th,
.rich-content table td {
  border: 1px solid rgb(229 231 235); /* gray-200 */
  padding: 0.75rem;
  text-align: left;
  white-space: normal;
}

.rich-content table th {
  font-weight: 600;
  color: rgb(17 24 39); /* gray-900 */
}

.rich-content table td {
  color: rgb(55 65 81); /* gray-700 */
}

/* 优化列表样式 */
.rich-content ul,
.rich-content ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.rich-content li {
  margin: 0.5rem 0;
  line-height: 1.6;
}

/* 优化引用块样式 */
.rich-content blockquote {
  border-left: 4px solid rgb(209 213 219); /* gray-300 */
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: rgb(75 85 99); /* gray-600 */
  background-color: rgb(249 250 251); /* gray-50 */
  padding: 1rem;
  border-radius: 0.375rem;
}

/* 优化代码块样式 */
.rich-content pre {
  background-color: rgb(249 250 251); /* gray-50 */
  border: 1px solid rgb(229 231 235); /* gray-200 */
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.rich-content code {
  background-color: rgb(243 244 246); /* gray-100 */
  color: rgb(31 41 55); /* gray-800 */
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.rich-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

/* 优化图片样式 */
.rich-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  margin: 1.5rem auto;
  display: block;
}

/* 优化分割线样式 */
.rich-content hr {
  border: none;
  border-top: 1px solid rgb(229 231 235); /* gray-200 */
  margin: 2rem 0;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .rich-content table {
    font-size: 0.875rem;
  }
  
  .rich-content table th,
  .rich-content table td {
    padding: 0.5rem;
  }
  
  .rich-content pre {
    padding: 0.75rem;
    font-size: 0.875rem;
  }
  
  .rich-content blockquote {
    padding: 0.75rem;
    margin: 1rem 0;
  }
}
