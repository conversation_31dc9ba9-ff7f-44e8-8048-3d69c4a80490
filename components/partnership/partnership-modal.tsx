"use client"

import React, { useState } from 'react';
import { Modal, Form, Input, Select, Button, message, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;

interface PartnershipModalProps {
  visible: boolean;
  onClose: () => void;
}

interface PartnershipFormData {
  companyName: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  companyWebsite?: string;
  companyScale: string;
  businessType: string;
  cooperationDescription: string;
  expectedCooperation: string;
}

export function PartnershipModal({ visible, onClose }: PartnershipModalProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: PartnershipFormData) => {
    setLoading(true);
    try {
      // 这里应该调用实际的API提交合作申请
      console.log('合作申请数据:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('合作申请提交成功！我们会在3个工作日内与您联系。');
      form.resetFields();
      onClose();
    } catch (error) {
      message.error('提交失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const companyScaleOptions = [
    { value: 'startup', label: '初创企业（1-50人）' },
    { value: 'small', label: '小型企业（51-200人）' },
    { value: 'medium', label: '中型企业（201-1000人）' },
    { value: 'large', label: '大型企业（1000人以上）' },
  ];

  const businessTypeOptions = [
    { value: 'internet', label: '互联网/软件' },
    { value: 'finance', label: '金融/保险' },
    { value: 'ecommerce', label: '电商/零售' },
    { value: 'education', label: '教育/培训' },
    { value: 'healthcare', label: '医疗/健康' },
    { value: 'manufacturing', label: '制造业' },
    { value: 'logistics', label: '物流/运输' },
    { value: 'other', label: '其他' },
  ];

  const cooperationTypeOptions = [
    { value: 'api_integration', label: 'API集成合作' },
    { value: 'white_label', label: '白标解决方案' },
    { value: 'strategic', label: '战略合作伙伴' },
    { value: 'reseller', label: '渠道代理' },
    { value: 'technical', label: '技术合作' },
    { value: 'other', label: '其他合作方式' },
  ];

  return (
    <Modal
      title="申请商务合作"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      destroyOnClose
    >
      <div className="mb-4">
        <p className="text-gray-600">
          感谢您对我们的关注！请填写以下信息，我们的商务团队会尽快与您联系。
        </p>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        requiredMark={false}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name="companyName"
            label="公司名称"
            rules={[{ required: true, message: '请输入公司名称' }]}
          >
            <Input placeholder="请输入公司全称" />
          </Form.Item>

          <Form.Item
            name="contactName"
            label="联系人姓名"
            rules={[{ required: true, message: '请输入联系人姓名' }]}
          >
            <Input placeholder="请输入您的姓名" />
          </Form.Item>

          <Form.Item
            name="contactPhone"
            label="联系电话"
            rules={[
              { required: true, message: '请输入联系电话' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
            ]}
          >
            <Input placeholder="请输入手机号码" />
          </Form.Item>

          <Form.Item
            name="contactEmail"
            label="邮箱地址"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入正确的邮箱格式' }
            ]}
          >
            <Input placeholder="请输入邮箱地址" />
          </Form.Item>

          <Form.Item
            name="companyWebsite"
            label="公司官网"
          >
            <Input placeholder="https://www.example.com" />
          </Form.Item>

          <Form.Item
            name="companyScale"
            label="公司规模"
            rules={[{ required: true, message: '请选择公司规模' }]}
          >
            <Select placeholder="请选择公司规模">
              {companyScaleOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="businessType"
            label="业务类型"
            rules={[{ required: true, message: '请选择业务类型' }]}
          >
            <Select placeholder="请选择主要业务类型">
              {businessTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="expectedCooperation"
            label="期望合作方式"
            rules={[{ required: true, message: '请选择期望的合作方式' }]}
          >
            <Select placeholder="请选择期望的合作方式">
              {cooperationTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        <Form.Item
          name="cooperationDescription"
          label="合作需求描述"
          rules={[{ required: true, message: '请描述您的合作需求' }]}
        >
          <TextArea
            rows={4}
            placeholder="请详细描述您的合作需求、预期目标、业务场景等..."
            maxLength={500}
            showCount
          />
        </Form.Item>

        <div className="flex justify-end gap-3 mt-6">
          <Button onClick={onClose}>
            取消
          </Button>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            提交申请
          </Button>
        </div>
      </Form>
    </Modal>
  );
}
