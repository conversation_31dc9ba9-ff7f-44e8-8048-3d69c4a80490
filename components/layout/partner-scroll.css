/* 合作商滚动动画样式 */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-33.33%);
  }
}

@keyframes scroll-reverse {
  0% {
    transform: translateX(-33.33%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-scroll-reverse {
  animation: scroll-reverse 35s linear infinite;
}

.pause-animation:hover {
  animation-play-state: paused;
}

/* 多行滚动容器 */
.multi-row-scroll {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 动画延迟类 */
.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 渐变遮罩优化 */
.gradient-mask-left {
  background: linear-gradient(to right, white 0%, transparent 100%);
}

.gradient-mask-right {
  background: linear-gradient(to left, white 0%, transparent 100%);
}

/* 合作商卡片悬停效果 */
.partner-card {
  transition: all 0.3s ease;
}

.partner-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .animate-scroll {
    animation-duration: 20s;
  }

  .animate-scroll-reverse {
    animation-duration: 25s;
  }

  /* 移动端合作商卡片优化 */
  .partner-card {
    width: 160px !important;
    height: 80px !important;
    margin: 0 8px !important;
    padding: 8px !important;
  }

  .partner-card img {
    max-height: 32px !important;
  }

  .partner-card span {
    font-size: 10px !important;
  }
}

@media (max-width: 640px) {
  .animate-scroll {
    animation-duration: 15s;
  }

  .animate-scroll-reverse {
    animation-duration: 20s;
  }

  /* 小屏幕设备优化 */
  .partner-card {
    width: 140px !important;
    height: 70px !important;
    margin: 0 6px !important;
    padding: 6px !important;
  }

  .partner-card img {
    max-height: 28px !important;
  }

  .partner-card span {
    font-size: 9px !important;
    line-height: 1.2 !important;
  }

  /* 减少行间距 */
  .multi-row-scroll {
    gap: 0.5rem !important;
  }
}
