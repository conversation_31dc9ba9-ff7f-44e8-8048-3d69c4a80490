/* 合作商滚动动画样式 */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.pause-animation:hover {
  animation-play-state: paused;
}

/* 渐变遮罩优化 */
.gradient-mask-left {
  background: linear-gradient(to right, white 0%, transparent 100%);
}

.gradient-mask-right {
  background: linear-gradient(to left, white 0%, transparent 100%);
}

/* 合作商卡片悬停效果 */
.partner-card {
  transition: all 0.3s ease;
}

.partner-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .animate-scroll {
    animation-duration: 20s;
  }
}

@media (max-width: 640px) {
  .animate-scroll {
    animation-duration: 15s;
  }
}
