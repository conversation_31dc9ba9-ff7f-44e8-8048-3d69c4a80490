"use client"

import Image from "next/image";
import Link from "next/link";
import { useSiteConfig } from "@/components/provider/site-provider";
import { SiteConfig } from "@/app/types";
import { usePathname } from "next/navigation";
import { GitBranch, MessageCircle, Users } from "lucide-react";
import { useState } from "react";
import { PartnershipModal } from "@/components/partnership/partnership-modal";
import { defaultPartners, generatePartnerRows, partnerConfig } from "@/config/partners";
import "./partner-scroll.css";

// 定义类型接口
interface LinkExchange {
  name: string;
  link: string;
}

interface BottomMenuItem {
  id: string;
  name: string;
  children?: Array<{
    id: string;
    name: string;
    link?: string;
  }>;
}

interface MarketingFooterProps {
  linkExchanges?: LinkExchange[];
  bottomMenuList?: BottomMenuItem[];
}

// 现代化的 Footer 组件
interface ModernMarketingFooterProps {
  bottomMenuList?: BottomMenuItem[];
  linkExchanges?: LinkExchange[];
}

export function ModernMarketingFooter({ bottomMenuList, linkExchanges }: ModernMarketingFooterProps) {
  const siteConfig = useSiteConfig();

  const socialLinks = [
    {
      name: 'GitHub',
      href: 'https://github.com/alapi',
      icon: GitBranch,
    },
    {
      name: '社区',
      href: 'https://community.alapi.cn',
      icon: MessageCircle,
    },
    {
      name: '用户群',
      href: 'https://qm.qq.com/cgi-bin/qm/qr?k=xxx',
      icon: Users,
    },
  ];

  return (
    <footer className="border-t bg-white">
      <div className="w-full max-w-6xl mx-auto pt-12 md:pt-16 pb-2 md:pb-2 lg:px-0 px-4">
        <div className="grid grid-cols-2 gap-8 md:grid-cols-5">
          {/* Brand */}
          <div className="col-span-2 md:col-span-1">
            <Link href="/" className="flex items-center space-x-2 mb-4">
              <Image
                src={siteConfig.site_logo || "/images/logo/icon_logo.svg"}
                alt={siteConfig.site_name}
                width={100}
                height={100}
              // className="h-6 w-6"
              />
              {/* <span className="font-bold text-lg text-gray-900">{siteConfig.site_name}</span> */}
            </Link>
            <p className="text-sm text-gray-600 mb-4">
              {siteConfig.site_description || '为开发者提供优质的API服务'}
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                    aria-label={social.name}
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* 动态菜单列表 */}
          {bottomMenuList && bottomMenuList.length > 0 && (
            bottomMenuList.map((menu) => (
              <div key={menu.id}>
                <h3 className="font-semibold mb-4 text-gray-900">{menu.name}</h3>
                <ul className="space-y-2">
                  {menu.children && menu.children.length > 0 && menu.children.map((sub) => (
                    <li key={sub.id}>
                      {sub.link ? (
                        <a
                          href={sub.link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                          title={sub.name}
                        >
                          {sub.name}
                        </a>
                      ) : (
                        <span
                          className="text-sm text-gray-600"
                          title={sub.name}
                        >
                          {sub.name}
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            ))
          )}
        </div>

        {/* 友情链接 */}
        {linkExchanges && linkExchanges.length > 0 && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-gray-700 font-medium">友情链接:</span>
              <div className="flex flex-wrap gap-x-4 gap-y-2">
                {linkExchanges.map((exchange, index) => (
                  <a
                    key={index}
                    href={exchange.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                    title={exchange.name}
                  >
                    {exchange.name}
                  </a>
                ))}
              </div>
            </div>
          </div>
        )}

        <div className="mt-8 pt-6 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-500">
            © {new Date().getFullYear()} {siteConfig.site_name} All rights reserved.
          </p>
          <p className="text-sm text-gray-500 mt-2 md:mt-0">
            <a href='//beian.miit.gov.cn/' target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-gray-600 hover:text-gray-900 transition-colors">{siteConfig.site_icp}</a>
          </p>
        </div>
      </div>
    </footer>
  );
}



// 合作商展示组件
export function TeamUpFooter() {
  const siteConfig = useSiteConfig();
  const [partnershipModalVisible, setPartnershipModalVisible] = useState(false);

  // 使用配置文件中的数据生成合作商行数据
  const partnerRows = generatePartnerRows(defaultPartners, partnerConfig);

  return (
    <div className="w-full bg-gradient-to-b from-gray-50 to-white py-16">
      <div className="w-full max-w-6xl mx-auto lg:px-0 px-4">
        {/* 标题区域 */}
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-4xl font-bold text-gray-900 mb-4">
            与行业领军企业 共同选择 {siteConfig.site_name}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
            已有数千家企业选择我们的API服务，为他们的业务提供稳定可靠的技术支持
          </p>

          {/* 合作申请按钮 */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <button
              onClick={() => {
                window.open('https://alapi-cn.feishu.cn/share/base/form/shrcnGOlA8odDtEd3uJip4zHwke','_blank')
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
            >
              申请商务合作
            </button>
            <button className="border border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 font-semibold py-3 px-8 rounded-lg transition-colors duration-200">
              查看合作案例
            </button>
          </div>
        </div>

        {/* 合作商滚动展示区域 */}
        <div className="relative overflow-hidden py-8">
          {/* 渐变遮罩 */}
          <div className="absolute left-0 top-0 w-20 h-full gradient-mask-left z-10"></div>
          <div className="absolute right-0 top-0 w-20 h-full gradient-mask-right z-10"></div>

          {/* 多行滚动容器 */}
          <div className="multi-row-scroll">
            {partnerRows.map((rowPartners, rowIndex) => (
              <div
                key={`row-${rowIndex}`}
                className={`flex animate-scroll pause-animation ${
                  rowIndex % 2 === 1 ? 'animate-scroll-reverse' : ''
                }`}
                style={{
                  animationDelay: `${rowIndex * partnerConfig.animation.baseDelay}s`,
                  animationDuration: `${partnerConfig.animation.baseDuration + rowIndex * partnerConfig.animation.durationIncrement}s`
                }}
              >
                {rowPartners.map((partner) => (
                  <div
                    key={partner.id}
                    className="partner-card flex-shrink-0 w-48 h-24 mx-4 flex flex-col items-center justify-center bg-white rounded-lg shadow-sm border border-gray-100 p-3"
                  >
                    <div className="flex items-center justify-center h-12 mb-2">
                      <img
                        src={partner.logo}
                        alt={partner.name}
                        className="max-w-full max-h-full object-contain filter transition-all duration-300"
                        onError={(e) => {
                          // 如果图片加载失败，显示占位符
                          const target = e.currentTarget as HTMLImageElement;
                          if (target.src !== '/images/partners/placeholder.svg') {
                            target.src = '/images/partners/placeholder.svg';
                          }
                        }}
                      />
                    </div>
                    <span className="text-xs font-medium text-gray-700 text-center truncate w-full">
                      {partner.name}
                    </span>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* 统计数据 */}
        <div className="mt-16 grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">1000+</div>
            <div className="text-sm text-gray-600">合作企业</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">100亿+</div>
            <div className="text-sm text-gray-600">年调用次数</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">99.9%</div>
            <div className="text-sm text-gray-600">服务可用性</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
            <div className="text-sm text-gray-600">技术支持</div>
          </div>
        </div>
      </div>

      {/* 合作申请模态框 */}
      <PartnershipModal
        visible={partnershipModalVisible}
        onClose={() => setPartnershipModalVisible(false)}
      />
    </div>
  );
}


export function IntroduceFooter() {
  const siteConfig = useSiteConfig();
  return <>
    <div className="lg:pt-[50px] border-box bg-primary">
      <div className="w-full max-w-6xl mx-auto flex justify-center flex-wrap lg:px-0 px-4">
        <div className="text-right lg:mt-10 mt-10">
          <Image width={200} height={100}
            src={'/images/svg/all_white_logo.svg'}
            className="w-[200px]"
            alt="apispace-logo" />
        </div>
        <div className="lg:pl-[100px] lg:pb-[35px] lg:mx-0 mx-10 lg:mt-0 mt-10">
          <h2 className="text-[24px] text-white mb-[15px]">
            数百万开发者的共同选择，年均 API 调用次数超过 100 亿次 - {siteConfig.site_name}
          </h2>
          <div className="mb-[25px]">
            <Image width={500} height={200}
              src="/images/svg/feature.svg" alt="feature" />
          </div>
        </div>
      </div>
    </div>
  </>
}

interface CopyrightFooterProps {
  siteConfig: SiteConfig;
  bottomMenuList?: BottomMenuItem[];
  linkExchange?: LinkExchange[];
}

export function CopyrightFooter({ siteConfig, bottomMenuList, linkExchange }: CopyrightFooterProps) {
  return <>

    <footer className="bg-white dark:bg-gray-900">
      <div className="container p-6 mx-auto">
        <div className="lg:flex">
          <div className="w-full -mx-6 lg:w-2/5">
            <div className="px-6">
              <a href="/">
                <img className="w-auto h-7" src={siteConfig.site_logo} alt="" />
              </a>
              <p className="max-w-sm mt-2 text-gray-500 dark:text-gray-400">{siteConfig.site_description}</p>
            </div>
          </div>

          <div className="mt-6 lg:mt-0 lg:flex-1">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {bottomMenuList && bottomMenuList.length > 0 && bottomMenuList.map((menu) => {
                return <div key={menu.id}>
                  <h3 className="text-gray-700 uppercase dark:text-white">{menu.name}</h3>
                  {menu.children && menu.children.length > 0 && menu.children.map((sub) => {
                    return sub.link ? <a key={sub.id} href={sub.link}
                      className={'block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline'}
                      target={'_blank'} title={sub.name}>{sub.name}</a> :
                      <span key={sub.id}
                        className={'block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline'}
                        title={sub.name}>{sub.name}</span>
                  })}
                </div>
              })}
            </div>
          </div>
        </div>

        {linkExchange && linkExchange.length > 0 && <>
          <div className={'flex items-center mt-2 gap-x-2'}>
            <div>友情链接:</div>
            <div className={'flex items-center gap-x-2'}>
              {linkExchange.map((e, index) => {
                return <a key={index} href={e.link}
                  title={e.name}
                  className={'block text-sm text-gray-600 dark:text-gray-400 hover:underline'}
                  target={'_blank'}>{e.name}
                </a>
              })}
            </div>
          </div>
        </>}


        <hr className="h-px mb-6 mt-2 bg-gray-200 border-none dark:bg-gray-700" />
        <div className="flex  items-center">
          <p
            className="text-center text-gray-500 dark:text-gray-400 flex flex-col sm:flex-row sm:justify-center sm:items-center">
            <span className="sm:mr-2">© {siteConfig.site_name} {new Date().getFullYear()} - All rights reserved</span>
            {siteConfig.site_icp &&
              <a title={siteConfig.site_icp} className="text-gray-500 !text-xs hover:!text-[#1e65eb] sm:ml-auto"
                href="https://beian.miit.gov.cn" rel="nofollow">{siteConfig.site_icp}</a>}
          </p>
        </div>
      </div>
    </footer>
  </>
}

export function MarketingFooter({ linkExchanges, bottomMenuList }: MarketingFooterProps) {
  const siteConfig = useSiteConfig();
  const pathname = usePathname();

  return (
    <>
      {pathname.includes("purchase") ? (
        <CopyrightFooter siteConfig={siteConfig} linkExchange={linkExchanges} bottomMenuList={bottomMenuList} />
      ) : (
        <div>
          <TeamUpFooter />
          <IntroduceFooter />
          {/* 使用新的现代化 Footer */}
          <ModernMarketingFooter bottomMenuList={bottomMenuList} linkExchanges={linkExchanges} />

        </div>
      )}
    </>
  );
}