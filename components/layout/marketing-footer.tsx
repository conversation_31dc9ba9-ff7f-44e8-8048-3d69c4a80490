"use client"

import Image from "next/image";
import Link from "next/link";
import { useSiteConfig } from "@/components/provider/site-provider";
import { SiteConfig } from "@/app/types";
import { usePathname } from "next/navigation";
import { GitBranch, MessageCircle, Users } from "lucide-react";

// 现代化的 Footer 组件
export function ModernMarketingFooter() {
  const siteConfig = useSiteConfig();

  const footerLinks = {
    product: [
      { title: '产品功能', href: '/features' },
      { title: '价格方案', href: '/pricing' },
      { title: 'API文档', href: '/docs' },
      { title: '更新日志', href: '/changelog' },
    ],
    company: [
      { title: '关于我们', href: '/about' },
      { title: '技术博客', href: '/blog' },
      { title: '加入我们', href: '/careers' },
      { title: '联系我们', href: '/contact' },
    ],
    resources: [
      { title: '帮助中心', href: '/help' },
      { title: '开发者社区', href: '/community' },
      { title: '技术支持', href: '/support' },
      { title: '服务状态', href: '/status' },
    ],
    legal: [
      { title: '隐私政策', href: '/privacy' },
      { title: '服务条款', href: '/terms' },
      { title: '安全说明', href: '/security' },
      { title: 'Cookie政策', href: '/cookies' },
    ],
  };

  const socialLinks = [
    {
      name: 'GitHub',
      href: 'https://github.com/alapi',
      icon: GitBranch,
    },
    {
      name: '社区',
      href: 'https://community.alapi.cn',
      icon: MessageCircle,
    },
    {
      name: '用户群',
      href: 'https://qm.qq.com/cgi-bin/qm/qr?k=xxx',
      icon: Users,
    },
  ];

  return (
    <footer className="border-t bg-white">
      <div className="w-full max-w-6xl mx-auto py-12 md:py-16 lg:px-0 px-4">
        <div className="grid grid-cols-2 gap-8 md:grid-cols-5">
          {/* Brand */}
          <div className="col-span-2 md:col-span-1">
            <Link href="/" className="flex items-center space-x-2 mb-4">
              <Image
                src={siteConfig.site_logo || "/images/logo/icon_logo.svg"}
                alt={siteConfig.site_name}
                width={24}
                height={24}
                className="h-6 w-6"
              />
              <span className="font-bold text-lg text-gray-900">{siteConfig.site_name}</span>
            </Link>
            <p className="text-sm text-gray-600 mb-4">
              {siteConfig.site_description || '为开发者提供优质的API服务'}
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                    aria-label={social.name}
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Product */}
          <div>
            <h3 className="font-semibold mb-4 text-gray-900">产品</h3>
            <ul className="space-y-2">
              {footerLinks.product.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold mb-4 text-gray-900">公司</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="font-semibold mb-4 text-gray-900">资源</h3>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h3 className="font-semibold mb-4 text-gray-900">法律</h3>
            <ul className="space-y-2">
              {footerLinks.legal.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-500">
            © {new Date().getFullYear()} {siteConfig.site_name}. 保留所有权利
          </p>
          <p className="text-sm text-gray-500 mt-2 md:mt-0">
            用 ❤️ 构建，基于 React 和 Next.js
          </p>
        </div>
      </div>
    </footer>
  );
}

export function TeamUpFooter() {
  let siteConfig = useSiteConfig();

  return <>
    {/* 合作展示开始 */}
    <div className="w-full max-w-6xl mx-auto text-center h-[280px] lg:h-[580px] justify-center mt-10 z-0 lg:px-0 px-4">
      <div className="text-xl font-bold	lg:text-4xl mb-5">
        与行业领军企业 共同选择{siteConfig.site_name}
      </div>
      <div
        className="bg-autoFull	lg:h-[480px] h-[200px] bg-top	bg-no-repeat lg:bg-enterprisePC bg-enterpriseMobile">
      </div>
    </div>
    {/* 合作展示结束 */}
  </>
}

// export function IntroduceFooter() {
//   let siteConfig = useSiteConfig();
//   const {openModal} = useAuthModal();
//
//
//   return <>
//     <div className="lg:pt-[50px]  border-box bg-primary ">
//       <div className="max-w-6xl mx-auto flex justify-center flex-wrap">
//         <div className="lg:pb-[35px] lg:mx-0 mx-10 lg:mt-0 mt-10"><h2
//           className="text-[24px] text-white mb-[15px]">免费试用100+接口，立即开启你的数据之旅</h2>
//           <div className="mb-[25px]"><Image width={500} height={200}
//                                             src="/images/svg/feature.svg" alt="feature"/>
//           </div>
//           <div><Button className={'w-full'} type={'default'} ghost onClick={openModal}>立即注册</Button></div>
//         </div>
//       </div>
//     </div>
//
//   </>
// }

export function IntroduceFooter() {
  const siteConfig = useSiteConfig();
  return <>
    <div className="lg:pt-[50px] border-box bg-primary">
      <div className="w-full max-w-6xl mx-auto flex justify-center flex-wrap lg:px-0 px-4">
        <div className="text-right lg:mt-10 mt-10">
          <Image width={200} height={100}
            src={'/images/svg/all_white_logo.svg'}
            className="w-[200px]"
            alt="apispace-logo" />
        </div>
        <div className="lg:pl-[100px] lg:pb-[35px] lg:mx-0 mx-10 lg:mt-0 mt-10">
          <h2 className="text-[24px] text-white mb-[15px]">
            数百万开发者的共同选择，年均 API 调用次数超过 100 亿次 - {siteConfig.site_name}
          </h2>
          <div className="mb-[25px]">
            <Image width={500} height={200}
              src="/images/svg/feature.svg" alt="feature" />
          </div>
        </div>
      </div>
    </div>
  </>
}

export function MenuFooter() {
  return <>
    <footer className="bg-white dark:bg-gray-900">
      <div className="container p-6 mx-auto">
        <div className="lg:flex">
          <div className="w-full -mx-6 lg:w-2/5">
            <div className="px-6">
              <a href="#">
                <img className="w-auto h-7" src="https://merakiui.com/images/full-logo.svg" alt="" />
              </a>

              <p className="max-w-sm mt-2 text-gray-500 dark:text-gray-400">Join 31,000+ other and never miss out on new
                tips, tutorials, and more.</p>

              <div className="flex mt-6 -mx-2">
                <a href="#"
                  className="mx-2 text-gray-600 transition-colors duration-300 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400"
                  aria-label="Reddit">
                  <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C21.9939 17.5203 17.5203 21.9939 12 22ZM6.807 10.543C6.20862 10.5433 5.67102 10.9088 5.45054 11.465C5.23006 12.0213 5.37133 12.6558 5.807 13.066C5.92217 13.1751 6.05463 13.2643 6.199 13.33C6.18644 13.4761 6.18644 13.6229 6.199 13.769C6.199 16.009 8.814 17.831 12.028 17.831C15.242 17.831 17.858 16.009 17.858 13.769C17.8696 13.6229 17.8696 13.4761 17.858 13.33C18.4649 13.0351 18.786 12.3585 18.6305 11.7019C18.475 11.0453 17.8847 10.5844 17.21 10.593H17.157C16.7988 10.6062 16.458 10.7512 16.2 11C15.0625 10.2265 13.7252 9.79927 12.35 9.77L13 6.65L15.138 7.1C15.1931 7.60706 15.621 7.99141 16.131 7.992C16.1674 7.99196 16.2038 7.98995 16.24 7.986C16.7702 7.93278 17.1655 7.47314 17.1389 6.94094C17.1122 6.40873 16.6729 5.991 16.14 5.991C16.1022 5.99191 16.0645 5.99491 16.027 6C15.71 6.03367 15.4281 6.21641 15.268 6.492L12.82 6C12.7983 5.99535 12.7762 5.993 12.754 5.993C12.6094 5.99472 12.4851 6.09583 12.454 6.237L11.706 9.71C10.3138 9.7297 8.95795 10.157 7.806 10.939C7.53601 10.6839 7.17843 10.5422 6.807 10.543ZM12.18 16.524C12.124 16.524 12.067 16.524 12.011 16.524C11.955 16.524 11.898 16.524 11.842 16.524C11.0121 16.5208 10.2054 16.2497 9.542 15.751C9.49626 15.6958 9.47445 15.6246 9.4814 15.5533C9.48834 15.482 9.52348 15.4163 9.579 15.371C9.62737 15.3318 9.68771 15.3102 9.75 15.31C9.81233 15.31 9.87275 15.3315 9.921 15.371C10.4816 15.7818 11.159 16.0022 11.854 16C11.9027 16 11.9513 16 12 16C12.059 16 12.119 16 12.178 16C12.864 16.0011 13.5329 15.7863 14.09 15.386C14.1427 15.3322 14.2147 15.302 14.29 15.302C14.3653 15.302 14.4373 15.3322 14.49 15.386C14.5985 15.4981 14.5962 15.6767 14.485 15.786V15.746C13.8213 16.2481 13.0123 16.5208 12.18 16.523V16.524ZM14.307 14.08H14.291L14.299 14.041C13.8591 14.011 13.4994 13.6789 13.4343 13.2429C13.3691 12.8068 13.6162 12.3842 14.028 12.2269C14.4399 12.0697 14.9058 12.2202 15.1478 12.5887C15.3899 12.9572 15.3429 13.4445 15.035 13.76C14.856 13.9554 14.6059 14.0707 14.341 14.08H14.306H14.307ZM9.67 14C9.11772 14 8.67 13.5523 8.67 13C8.67 12.4477 9.11772 12 9.67 12C10.2223 12 10.67 12.4477 10.67 13C10.67 13.5523 10.2223 14 9.67 14Z">
                    </path>
                  </svg>
                </a>

                <a href="#"
                  className="mx-2 text-gray-600 transition-colors duration-300 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400"
                  aria-label="Facebook">
                  <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M2.00195 12.002C2.00312 16.9214 5.58036 21.1101 10.439 21.881V14.892H7.90195V12.002H10.442V9.80204C10.3284 8.75958 10.6845 7.72064 11.4136 6.96698C12.1427 6.21332 13.1693 5.82306 14.215 5.90204C14.9655 5.91417 15.7141 5.98101 16.455 6.10205V8.56104H15.191C14.7558 8.50405 14.3183 8.64777 14.0017 8.95171C13.6851 9.25566 13.5237 9.68693 13.563 10.124V12.002H16.334L15.891 14.893H13.563V21.881C18.8174 21.0506 22.502 16.2518 21.9475 10.9611C21.3929 5.67041 16.7932 1.73997 11.4808 2.01722C6.16831 2.29447 2.0028 6.68235 2.00195 12.002Z">
                    </path>
                  </svg>
                </a>

                <a href="#"
                  className="mx-2 text-gray-600 transition-colors duration-300 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400"
                  aria-label="Github">
                  <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M12.026 2C7.13295 1.99937 2.96183 5.54799 2.17842 10.3779C1.395 15.2079 4.23061 19.893 8.87302 21.439C9.37302 21.529 9.55202 21.222 9.55202 20.958C9.55202 20.721 9.54402 20.093 9.54102 19.258C6.76602 19.858 6.18002 17.92 6.18002 17.92C5.99733 17.317 5.60459 16.7993 5.07302 16.461C4.17302 15.842 5.14202 15.856 5.14202 15.856C5.78269 15.9438 6.34657 16.3235 6.66902 16.884C6.94195 17.3803 7.40177 17.747 7.94632 17.9026C8.49087 18.0583 9.07503 17.99 9.56902 17.713C9.61544 17.207 9.84055 16.7341 10.204 16.379C7.99002 16.128 5.66202 15.272 5.66202 11.449C5.64973 10.4602 6.01691 9.5043 6.68802 8.778C6.38437 7.91731 6.42013 6.97325 6.78802 6.138C6.78802 6.138 7.62502 5.869 9.53002 7.159C11.1639 6.71101 12.8882 6.71101 14.522 7.159C16.428 5.868 17.264 6.138 17.264 6.138C17.6336 6.97286 17.6694 7.91757 17.364 8.778C18.0376 9.50423 18.4045 10.4626 18.388 11.453C18.388 15.286 16.058 16.128 13.836 16.375C14.3153 16.8651 14.5612 17.5373 14.511 18.221C14.511 19.555 14.499 20.631 14.499 20.958C14.499 21.225 14.677 21.535 15.186 21.437C19.8265 19.8884 22.6591 15.203 21.874 10.3743C21.089 5.54565 16.9181 1.99888 12.026 2Z">
                    </path>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          <div className="mt-6 lg:mt-0 lg:flex-1">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              <div>
                <h3 className="text-gray-700 uppercase dark:text-white">About</h3>
                <a href="#" className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">Company</a>
                <a href="#"
                  className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">community</a>
                <a href="#" className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">Careers</a>
              </div>

              <div>
                <h3 className="text-gray-700 uppercase dark:text-white">Blog</h3>
                <a href="#" className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">Tec</a>
                <a href="#" className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">Music</a>
                <a href="#" className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">Videos</a>
              </div>

              <div>
                <h3 className="text-gray-700 uppercase dark:text-white">Products</h3>
                <a href="#" className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">Mega
                  cloud</a>
                <a href="#" className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">Aperion
                  UI</a>
                <a href="#" className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">Meraki
                  UI</a>
              </div>

              <div>
                <h3 className="text-gray-700 uppercase dark:text-white">Contact</h3>
                <span
                  className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline">+1 526 654 8965</span>
                <span
                  className="block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        <hr className="h-px my-6 bg-gray-200 border-none dark:bg-gray-700" />

        <div>
          <p className="text-center text-gray-500 dark:text-gray-400">© Brand 2020 - All rights reserved</p>
        </div>
      </div>
    </footer>
  </>
}

interface CopyrightFooterProps {
  siteConfig: SiteConfig;
  bottomMenuList?: BottomMenuItem[];
  linkExchange?: LinkExchange[];
}

export function CopyrightFooter({ siteConfig, bottomMenuList, linkExchange }: CopyrightFooterProps) {
  return <>

    <footer className="bg-white dark:bg-gray-900">
      <div className="container p-6 mx-auto">
        <div className="lg:flex">
          <div className="w-full -mx-6 lg:w-2/5">
            <div className="px-6">
              <a href="/">
                <img className="w-auto h-7" src={siteConfig.site_logo} alt="" />
              </a>
              <p className="max-w-sm mt-2 text-gray-500 dark:text-gray-400">{siteConfig.site_description}</p>
            </div>
          </div>

          <div className="mt-6 lg:mt-0 lg:flex-1">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {bottomMenuList && bottomMenuList.length > 0 && bottomMenuList.map((menu) => {
                return <div key={menu.id}>
                  <h3 className="text-gray-700 uppercase dark:text-white">{menu.name}</h3>
                  {menu.children && menu.children.length > 0 && menu.children.map((sub) => {
                    return sub.link ? <a key={sub.id} href={sub.link}
                      className={'block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline'}
                      target={'_blank'} title={sub.name}>{sub.name}</a> :
                      <span key={sub.id}
                        className={'block mt-2 text-sm text-gray-600 dark:text-gray-400 hover:underline'}
                        title={sub.name}>{sub.name}</span>
                  })}
                </div>
              })}
            </div>
          </div>
        </div>

        {linkExchange && linkExchange.length > 0 && <>
          <div className={'flex items-center mt-2 gap-x-2'}>
            <div>友情链接:</div>
            <div className={'flex items-center gap-x-2'}>
              {linkExchange.map((e, index) => {
                return <a key={index} href={e.link}
                  title={e.name}
                  className={'block text-sm text-gray-600 dark:text-gray-400 hover:underline'}
                  target={'_blank'}>{e.name}
                </a>
              })}
            </div>
          </div>
        </>}


        <hr className="h-px mb-6 mt-2 bg-gray-200 border-none dark:bg-gray-700" />
        <div className="flex  items-center">
          <p
            className="text-center text-gray-500 dark:text-gray-400 flex flex-col sm:flex-row sm:justify-center sm:items-center">
            <span className="sm:mr-2">© {siteConfig.site_name} {new Date().getFullYear()} - All rights reserved</span>
            {siteConfig.site_icp &&
              <a title={siteConfig.site_icp} className="text-gray-500 !text-xs hover:!text-[#1e65eb] sm:ml-auto"
                href="https://beian.miit.gov.cn" rel="nofollow">{siteConfig.site_icp}</a>}
          </p>
        </div>
      </div>
    </footer>
  </>
}

// 定义类型接口
interface LinkExchange {
  name: string;
  link: string;
}

interface BottomMenuItem {
  id: string;
  name: string;
  children?: Array<{
    id: string;
    name: string;
    link?: string;
  }>;
}

interface MarketingFooterProps {
  linkExchanges?: LinkExchange[];
  bottomMenuList?: BottomMenuItem[];
}

export function MarketingFooter({ linkExchanges, bottomMenuList }: MarketingFooterProps) {
  const siteConfig = useSiteConfig();
  const pathname = usePathname();

  return (
    <>
      {pathname.includes("purchase") ? (
        <CopyrightFooter siteConfig={siteConfig} linkExchange={linkExchanges} bottomMenuList={bottomMenuList} />
      ) : (
        <div>
          <TeamUpFooter />
          <IntroduceFooter />
          {/* 使用新的现代化 Footer */}
          <ModernMarketingFooter />

        </div>
      )}
    </>
  );
}