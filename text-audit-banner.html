<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本审核API - Banner</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .banner-container {
            width: 800px;
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 60px;
        }

        /* 背景装饰元素 */
        .banner-container::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 400px;
            height: 400px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
        }

        .banner-container::after {
            content: '';
            position: absolute;
            bottom: -30%;
            left: -10%;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
            border-radius: 50%;
        }

        .banner-content {
            flex: 1;
            color: white;
            z-index: 2;
            position: relative;
        }

        .api-logo {
            font-size: 20px;
            font-weight: bold;
            color: #4A90E2;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 16px;
            border-radius: 20px;
            width: fit-content;
        }

        .api-logo::before {
            content: "🔍";
            margin-right: 8px;
            font-size: 18px;
        }

        .banner-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 16px;
            line-height: 1.2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .banner-subtitle {
            font-size: 16px;
            margin-bottom: 24px;
            opacity: 0.9;
            line-height: 1.4;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 20px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .banner-visual {
            flex: 0 0 280px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            position: relative;
        }

        .phone-mockup {
            width: 180px;
            height: 320px;
            background: #4A90E2;
            border-radius: 24px;
            position: relative;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            transform: rotate(-5deg);
        }

        .phone-screen {
            width: 150px;
            height: 260px;
            background: white;
            border-radius: 18px;
            position: absolute;
            top: 30px;
            left: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow: hidden;
        }

        .phone-notch {
            width: 50px;
            height: 5px;
            background: #4A90E2;
            border-radius: 3px;
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
        }

        .phone-button {
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
        }

        .screen-content {
            text-align: center;
            color: #333;
        }

        .screen-icon {
            font-size: 60px;
            margin-bottom: 16px;
            animation: pulse 2s infinite;
        }

        .screen-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #4A90E2;
        }

        .screen-status {
            font-size: 12px;
            color: #666;
            margin-bottom: 16px;
        }

        .detection-items {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            justify-content: center;
        }

        .detection-tag {
            font-size: 8px;
            padding: 2px 6px;
            background: #f0f8ff;
            border: 1px solid #4A90E2;
            border-radius: 10px;
            color: #4A90E2;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 浮动元素 */
        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-1 {
            width: 60px;
            height: 60px;
            top: 10%;
            left: 15%;
            animation-delay: 0s;
        }

        .floating-2 {
            width: 40px;
            height: 40px;
            top: 70%;
            right: 20%;
            animation-delay: 2s;
        }

        .floating-3 {
            width: 30px;
            height: 30px;
            top: 30%;
            right: 10%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(10px) rotate(240deg); }
        }

        /* 响应式调整 */
        @media (max-width: 900px) {
            .banner-container {
                width: 90vw;
                height: 300px;
                padding: 0 40px;
            }

            .banner-title {
                font-size: 28px;
            }

            .phone-mockup {
                width: 140px;
                height: 250px;
            }

            .phone-screen {
                width: 115px;
                height: 200px;
                top: 25px;
                left: 12px;
            }

            .screen-icon {
                font-size: 45px;
            }
        }
    </style>
</head>
<body>
    <div class="banner-container">
        <!-- 浮动装饰元素 -->
        <div class="floating-element floating-1"></div>
        <div class="floating-element floating-2"></div>
        <div class="floating-element floating-3"></div>

        <!-- 左侧内容 -->
        <div class="banner-content">
            <div class="api-logo">ALAPI</div>
            <h1 class="banner-title">文本审核API</h1>
            <p class="banner-subtitle">智能检测文本内容违规信息，保护平台内容安全</p>
            
            <div class="features-grid">
                <div class="feature-item">
                    <span class="feature-icon">🚫</span>
                    <span>色情文字检测</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📢</span>
                    <span>广告文字过滤</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">⚠️</span>
                    <span>敏感内容识别</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🏛️</span>
                    <span>涉政内容监控</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">💬</span>
                    <span>灌水文字拦截</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🛡️</span>
                    <span>低俗辱骂过滤</span>
                </div>
            </div>
        </div>

        <!-- 右侧手机模型 -->
        <div class="banner-visual">
            <div class="phone-mockup">
                <div class="phone-notch"></div>
                <div class="phone-screen">
                    <div class="screen-content">
                        <div class="screen-icon">🛡️</div>
                        <div class="screen-title">文本安全检测</div>
                        <div class="screen-status">实时审核中...</div>
                        <div class="detection-items">
                            <span class="detection-tag">色情</span>
                            <span class="detection-tag">广告</span>
                            <span class="detection-tag">敏感</span>
                            <span class="detection-tag">涉政</span>
                            <span class="detection-tag">灌水</span>
                            <span class="detection-tag">辱骂</span>
                        </div>
                    </div>
                </div>
                <div class="phone-button"></div>
            </div>
        </div>
    </div>
</body>
</html>
