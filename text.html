<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本审核API - 轮播展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .carousel-container {
            width: 100%;
            max-width: 900px;
            height: 500px;
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .carousel-slide {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transition: opacity 0.8s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 60px;
            color: white;
        }

        .carousel-slide.active {
            opacity: 1;
        }

        .slide-content {
            flex: 1;
            max-width: 50%;
        }

        .api-logo {
            font-size: 24px;
            font-weight: bold;
            color: #4A90E2;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
        }

        .api-logo::before {
            content: "🔍";
            margin-right: 10px;
            font-size: 28px;
        }

        .slide-title {
            font-size: 42px;
            font-weight: bold;
            margin-bottom: 30px;
            line-height: 1.2;
        }

        .slide-features {
            list-style: none;
        }

        .slide-features li {
            font-size: 18px;
            margin-bottom: 15px;
            padding-left: 25px;
            position: relative;
            line-height: 1.5;
        }

        .slide-features li::before {
            content: "•";
            color: #4A90E2;
            font-size: 20px;
            position: absolute;
            left: 0;
            top: 0;
        }

        .slide-visual {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 40%;
        }

        .phone-mockup {
            width: 200px;
            height: 350px;
            background: #4A90E2;
            border-radius: 25px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .phone-screen {
            width: 160px;
            height: 280px;
            background: white;
            border-radius: 15px;
            position: absolute;
            top: 35px;
            left: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .phone-notch {
            width: 60px;
            height: 6px;
            background: #4A90E2;
            border-radius: 3px;
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
        }

        .phone-button {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 不同滑块的背景色 */
        .slide-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .slide-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .slide-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .slide-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .slide-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .slide-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

        /* 轮播指示器 */
        .carousel-indicators {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .indicator.active {
            background: white;
        }

        /* 图标样式 */
        .detection-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .detection-text {
            font-size: 16px;
            color: #333;
            text-align: center;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .carousel-slide {
                flex-direction: column;
                padding: 40px 30px;
                text-align: center;
            }

            .slide-content {
                max-width: 100%;
                margin-bottom: 30px;
            }

            .slide-visual {
                max-width: 100%;
            }

            .slide-title {
                font-size: 32px;
            }

            .slide-features li {
                font-size: 16px;
            }

            .phone-mockup {
                width: 160px;
                height: 280px;
            }

            .phone-screen {
                width: 130px;
                height: 220px;
                top: 30px;
                left: 15px;
            }

            .detection-icon {
                font-size: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="carousel-container">
        <!-- 滑块1: 总览 -->
        <div class="carousel-slide slide-1 active">
            <div class="slide-content">
                <div class="api-logo">ALAPI</div>
                <h2 class="slide-title">文本审核API</h2>
                <ul class="slide-features">
                    <li>智能检测多种违规文本内容</li>
                    <li>支持实时审核，毫秒级响应</li>
                    <li>准确率高达99.5%，持续优化</li>
                </ul>
            </div>
            <div class="slide-visual">
                <div class="phone-mockup">
                    <div class="phone-notch"></div>
                    <div class="phone-screen">
                        <div class="detection-icon">🛡️</div>
                        <div class="detection-text">文本安全检测</div>
                    </div>
                    <div class="phone-button"></div>
                </div>
            </div>
        </div>

        <!-- 滑块2: 色情文字检测 -->
        <div class="carousel-slide slide-2">
            <div class="slide-content">
                <div class="api-logo">ALAPI</div>
                <h2 class="slide-title">色情文字检测</h2>
                <ul class="slide-features">
                    <li>识别色情、低俗等不当内容</li>
                    <li>支持多种表达方式和变体检测</li>
                    <li>保护平台内容健康安全</li>
                </ul>
            </div>
            <div class="slide-visual">
                <div class="phone-mockup">
                    <div class="phone-notch"></div>
                    <div class="phone-screen">
                        <div class="detection-icon">🚫</div>
                        <div class="detection-text">色情内容拦截</div>
                    </div>
                    <div class="phone-button"></div>
                </div>
            </div>
        </div>

        <!-- 滑块3: 广告文字检测 -->
        <div class="carousel-slide slide-3">
            <div class="slide-content">
                <div class="api-logo">ALAPI</div>
                <h2 class="slide-title">广告文字检测</h2>
                <ul class="slide-features">
                    <li>识别垃圾广告和营销信息</li>
                    <li>检测联系方式、推广链接</li>
                    <li>维护社区讨论环境纯净</li>
                </ul>
            </div>
            <div class="slide-visual">
                <div class="phone-mockup">
                    <div class="phone-notch"></div>
                    <div class="phone-screen">
                        <div class="detection-icon">📢</div>
                        <div class="detection-text">广告内容过滤</div>
                    </div>
                    <div class="phone-button"></div>
                </div>
            </div>
        </div>

        <!-- 滑块4: 敏感文字检测 -->
        <div class="carousel-slide slide-4">
            <div class="slide-content">
                <div class="api-logo">ALAPI</div>
                <h2 class="slide-title">敏感文字检测</h2>
                <ul class="slide-features">
                    <li>检测暴力、恐怖等敏感内容</li>
                    <li>识别可能引起争议的话题</li>
                    <li>确保内容符合平台规范</li>
                </ul>
            </div>
            <div class="slide-visual">
                <div class="phone-mockup">
                    <div class="phone-notch"></div>
                    <div class="phone-screen">
                        <div class="detection-icon">⚠️</div>
                        <div class="detection-text">敏感内容识别</div>
                    </div>
                    <div class="phone-button"></div>
                </div>
            </div>
        </div>

        <!-- 滑块5: 涉政文字检测 -->
        <div class="carousel-slide slide-5">
            <div class="slide-content">
                <div class="api-logo">ALAPI</div>
                <h2 class="slide-title">涉政文字检测</h2>
                <ul class="slide-features">
                    <li>识别政治敏感和违法内容</li>
                    <li>符合国家法律法规要求</li>
                    <li>保障平台合规运营</li>
                </ul>
            </div>
            <div class="slide-visual">
                <div class="phone-mockup">
                    <div class="phone-notch"></div>
                    <div class="phone-screen">
                        <div class="detection-icon">🏛️</div>
                        <div class="detection-text">涉政内容监控</div>
                    </div>
                    <div class="phone-button"></div>
                </div>
            </div>
        </div>

        <!-- 滑块6: 灌水辱骂检测 -->
        <div class="carousel-slide slide-6">
            <div class="slide-content">
                <div class="api-logo">ALAPI</div>
                <h2 class="slide-title">灌水辱骂检测</h2>
                <ul class="slide-features">
                    <li>识别恶意灌水和人身攻击</li>
                    <li>检测低俗辱骂和不文明用语</li>
                    <li>营造和谐的交流环境</li>
                </ul>
            </div>
            <div class="slide-visual">
                <div class="phone-mockup">
                    <div class="phone-notch"></div>
                    <div class="phone-screen">
                        <div class="detection-icon">💬</div>
                        <div class="detection-text">不当言论过滤</div>
                    </div>
                    <div class="phone-button"></div>
                </div>
            </div>
        </div>

        <!-- 轮播指示器 -->
        <div class="carousel-indicators">
            <div class="indicator active" data-slide="0"></div>
            <div class="indicator" data-slide="1"></div>
            <div class="indicator" data-slide="2"></div>
            <div class="indicator" data-slide="3"></div>
            <div class="indicator" data-slide="4"></div>
            <div class="indicator" data-slide="5"></div>
        </div>
    </div>

    <script>
        class TextAuditCarousel {
            constructor() {
                this.slides = document.querySelectorAll('.carousel-slide');
                this.indicators = document.querySelectorAll('.indicator');
                this.currentSlide = 0;
                this.slideInterval = null;
                
                this.init();
            }

            init() {
                // 绑定指示器点击事件
                this.indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', () => {
                        this.goToSlide(index);
                    });
                });

                // 开始自动轮播
                this.startAutoPlay();

                // 鼠标悬停时暂停轮播
                const container = document.querySelector('.carousel-container');
                container.addEventListener('mouseenter', () => this.stopAutoPlay());
                container.addEventListener('mouseleave', () => this.startAutoPlay());
            }

            goToSlide(index) {
                // 移除当前活动状态
                this.slides[this.currentSlide].classList.remove('active');
                this.indicators[this.currentSlide].classList.remove('active');

                // 设置新的活动滑块
                this.currentSlide = index;
                this.slides[this.currentSlide].classList.add('active');
                this.indicators[this.currentSlide].classList.add('active');
            }

            nextSlide() {
                const nextIndex = (this.currentSlide + 1) % this.slides.length;
                this.goToSlide(nextIndex);
            }

            startAutoPlay() {
                this.slideInterval = setInterval(() => {
                    this.nextSlide();
                }, 4000); // 每4秒切换一次
            }

            stopAutoPlay() {
                if (this.slideInterval) {
                    clearInterval(this.slideInterval);
                    this.slideInterval = null;
                }
            }
        }

        // 页面加载完成后初始化轮播
        document.addEventListener('DOMContentLoaded', () => {
            new TextAuditCarousel();
        });
    </script>
</body>
</html>
